/**
 * 任务异常情况统计
 */
export interface TaskExceptionStats {
  /** 异常总数 */
  totalEventCount: number;
  /** 智慧检修异常数 */
  vehicleEventCount: number;
  /** 智慧作业异常数 */
  controlEventCount: number;
  /** 智慧料库异常数 */
  warehouseEventCount: number;
}

/**
 * 任务异常事件趋势数据项
 */
export interface TaskExceptionTrendItem {
  /** 日期字符串 */
  dateStr: string;
  /** 异常总数 */
  totalEventCount: number;
  /** 车辆检修异常数 */
  vehicleEventCount: number;
  /** 作业管控异常数 */
  controlEventCount: number;
  /** 料库异常数 */
  warehouseEventCount: number;
}

/**
 * 任务情况统计数据项
 */
export interface TaskStatisticsItem {
  /** 任务数量 */
  taskCount: number;
  /** 完成数量 */
  completeNum: number;
  /** 完成率 */
  completeRate: string;
  /** 平均持续时间（小时） */
  avgDurationHours: string;
  /** 任务类型 1-料库任务 2-作业管控任务 3-车辆检修任务 */
  taskType: string;
}

/**
 * 异常类型分布数据项
 */
export interface EventTypeDistributionItem {
  /** 事件类型名称 */
  eventName: string;
  /** 事件数量 */
  eventCount: number;
  /** 占比 */
  percentage: number;
}

/**
 * 站点任务趋势数据项
 */
export interface SiteTaskTrendItem {
  /** 任务总数 */
  totalCount: number;
  /** 日期 */
  xdata: string;
}

/**
 * 模型调用情况数据项（Mock数据）
 */
export interface ModelCallStatsItem {
  /** 模型名称 */
  modelName: string;
  /** 调用次数 */
  callCount: number;
  /** 成功率 */
  successRate: number;
  /** 日期 */
  date: string;
}

/**
 * 模型调用统计系列数据项
 */
export interface ModelCallSeriesItem {
  /** 数据数组 */
  data: number[];
  /** 模型名称 */
  name: string;
  /** 图表类型 */
  type: string;
}

/**
 * 模型调用统计响应数据
 */
export interface ModelCallStatsResponse {
  /** 系列数据 */
  series: ModelCallSeriesItem[];
  /** 日期数组 */
  timeStr: string[];
}

/**
 * 模型调用统计查询参数
 */
export interface ModelCallStatsQuery {
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 站点名称 */
  siteName?: string;
  /** 时间类型 */
  timeType?: string;
  /** 事件类型 */
  eventType?: string;
}

/**
 * 查询参数
 */
export interface TaskStatisticsQuery {
  /** 站点名称 */
  siteName?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 事件类型 */
  eventType?: string;
  /** 天数 */
  days?: string;
}
