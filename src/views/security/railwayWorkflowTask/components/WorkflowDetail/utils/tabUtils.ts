import { TAB_LABEL_MAP } from '../constants';
import type { TabItem, WorkflowStep } from '../types';

/**
 * 解析步骤的检测项，生成Tab列表
 */
export function parseDetectionItemToTabs(stepData: WorkflowStep | null): TabItem[] {
  if (!stepData?.detectionItem) return [];

  const items = stepData.detectionItem.split(',').filter((item) => item.trim());
  return items.map((item) => ({
    key: item.trim(),
    label: TAB_LABEL_MAP[item.trim() as keyof typeof TAB_LABEL_MAP] || item.trim(),
  }));
}

/**
 * 获取第一个有效的Tab Key
 */
export function getFirstTabKey(stepData: WorkflowStep | null): string {
  const tabs = parseDetectionItemToTabs(stepData);
  return tabs.length > 0 ? tabs[0].key : '';
}

/**
 * 判断是否为人员清点Tab
 */
export function isFaceRecognitionTab(tabKey: string): boolean {
  return tabKey === 'face';
}

/**
 * 判断是否为管理信息Tab
 */
export function isManagementInfoTab(tabKey: string): boolean {
  return [
    'helmet_workwear',
    'insulating_gloves_boots',
    'grounding_wire',
    'voltage_detection',
    'hang_grounding_pole',
  ].includes(tabKey);
}

/**
 * 判断是否为工具清点Tab
 */
export function isUtensilRecognitionTab(tabKey: string): boolean {
  return ['upper_utensil', 'lower_utensil'].includes(tabKey);
}

/**
 * 判断是否为上下道工具清点Tab
 */
export function isLowerUtensilTab(tabKey: string): boolean {
  return tabKey === 'lower_utensil';
}
