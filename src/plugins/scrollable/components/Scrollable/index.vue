<template>
  <div
    ref="container"
    class="overflow-auto scrollable"
    :class="{
      'cursor-grab': !isDragging,
      'cursor-grabbing': isDragging,
      'scrollable-horizontal': direction === 'horizontal',
      'scrollable-vertical': direction === 'vertical',
    }"
    @mousedown="startDrag"
    @mousemove="onDrag"
    @mouseup="endDrag"
    @mouseleave="handleMouseLeave"
    @wheel="onWheel"
    @mouseenter="showIndicators"
  >
    <!-- 滚动指示器 -->
    <div v-if="indicator" class="scroll-indicators" :class="{ show: showScrollIndicators }">
      <!-- 水平滚动指示器 -->
      <template v-if="direction === 'horizontal'">
        <!-- 左侧渐变遮罩 -->
        <div v-if="canScrollLeft" class="scroll-gradient scroll-gradient-left"></div>
        <!-- 右侧渐变遮罩 -->
        <div v-if="canScrollRight" class="scroll-gradient scroll-gradient-right"></div>
        <!-- 顶部滚动提示 -->
        <div class="scroll-hint scroll-hint-horizontal">
          <div class="scroll-hint-content">
            <svg class="scroll-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M5.5 12L11 17.5V14H20V10H11V6.5L5.5 12Z" />
            </svg>
            <span class="scroll-text">在此处滚轮滚动</span>
            <svg class="scroll-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M18.5 12L13 6.5V10H4V14H13V17.5L18.5 12Z" />
            </svg>
          </div>
        </div>
      </template>

      <!-- 垂直滚动指示器 -->
      <template v-if="direction === 'vertical'">
        <!-- 顶部渐变遮罩 -->
        <div v-if="canScrollUp" class="scroll-gradient scroll-gradient-top"></div>
        <!-- 底部渐变遮罩 -->
        <div v-if="canScrollDown" class="scroll-gradient scroll-gradient-bottom"></div>
        <!-- 左侧滚动提示 -->
        <div class="scroll-hint scroll-hint-vertical">
          <div class="scroll-hint-content">
            <svg class="scroll-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 18.5L17.5 13H14V4H10V13H6.5L12 18.5Z" />
            </svg>
            <span class="scroll-text">拖拽或滚轮滚动</span>
            <svg class="scroll-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 5.5L6.5 11H10V20H14V11H17.5L12 5.5Z" />
            </svg>
          </div>
        </div>
      </template>
    </div>

    <div
      ref="content"
      class="inline-flex min-h-12"
      :class="{ 'flex-col': direction === 'vertical' }"
      :style="contentStyle"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted } from 'vue';

  defineOptions({
    name: 'Scrollable',
  });

  const props = defineProps({
    direction: {
      type: String,
      default: 'horizontal',
      validator: (value) => ['horizontal', 'vertical'].includes(value),
    },
    speed: {
      type: Number,
      fault: 1,
    },
    disabledDrag: {
      type: Boolean,
      default: false,
    },
    indicator: {
      type: Boolean,
      default: true,
    },
  });

  const container = ref(null);
  const content = ref(null);
  const isDragging = ref(false);
  const startX = ref(0);
  const startY = ref(0);
  const scrollLeft = ref(0);
  const scrollTop = ref(0);
  const showScrollIndicators = ref(false);
  let indicatorTimeout = null;

  const contentStyle = computed(() => ({
    transform:
      props.direction === 'horizontal'
        ? `translateX(${-scrollLeft.value}px)`
        : `translateY(${-scrollTop.value}px)`,
    transition: isDragging.value ? 'none' : 'transform 0.3s ease-out',
  }));

  // 计算滚动状态
  const canScrollLeft = computed(() => {
    return scrollLeft.value > 0;
  });

  const canScrollRight = computed(() => {
    if (!content.value || !container.value) return false;
    return scrollLeft.value < content.value.offsetWidth - container.value.offsetWidth;
  });

  const canScrollUp = computed(() => {
    return scrollTop.value > 0;
  });

  const canScrollDown = computed(() => {
    if (!content.value || !container.value) return false;
    return scrollTop.value < content.value.offsetHeight - container.value.offsetHeight;
  });

  // 指示器控制函数
  const showIndicators = () => {
    showScrollIndicators.value = true;
    if (indicatorTimeout) {
      clearTimeout(indicatorTimeout);
    }
  };

  const hideIndicators = () => {
    indicatorTimeout = setTimeout(() => {
      showScrollIndicators.value = false;
    }, 1000); // 1秒后隐藏指示器
  };

  const handleMouseLeave = () => {
    endDrag();
    hideIndicators();
  };

  const startDrag = (e) => {
    if (props.disabledDrag) {
      return false;
    }

    isDragging.value = true;
    startX.value = e.pageX - container.value.offsetLeft;
    startY.value = e.pageY - container.value.offsetTop;
    container.value.style.cursor = 'grabbing';
  };

  const onDrag = (e) => {
    if (props.disabledDrag) {
      return false;
    }

    if (!isDragging.value) return;
    e.preventDefault();
    const x = e.pageX - container.value.offsetLeft;
    const y = e.pageY - container.value.offsetTop;
    const walkX = (x - startX.value) * props.speed;
    const walkY = (y - startY.value) * props.speed;

    if (props.direction === 'horizontal') {
      scrollLeft.value = Math.max(
        0,
        Math.min(scrollLeft.value - walkX, content.value.offsetWidth - container.value.offsetWidth),
      );
    } else {
      scrollTop.value = Math.max(
        0,
        Math.min(
          scrollTop.value - walkY,
          content.value.offsetHeight - container.value.offsetHeight,
        ),
      );
    }

    startX.value = x;
    startY.value = y;
  };

  const endDrag = () => {
    if (props.disabledDrag) {
      return false;
    }

    isDragging.value = false;
    container.value.style.cursor = 'grab';
  };

  const onWheel = (e) => {
    e.preventDefault();
    const delta = props.direction === 'horizontal' ? e.deltaX || e.deltaY : e.deltaY;
    const newScroll =
      (props.direction === 'horizontal' ? scrollLeft.value : scrollTop.value) + delta * props.speed;

    if (props.direction === 'horizontal') {
      scrollLeft.value = Math.max(
        0,
        Math.min(newScroll, content.value.offsetWidth - container.value.offsetWidth),
      );
    } else {
      scrollTop.value = Math.max(
        0,
        Math.min(newScroll, content.value.offsetHeight - container.value.offsetHeight),
      );
    }
  };

  const getIncrement = () => {
    return 100 * props.speed;
  };

  const scrollToEnd = () => {
    if (props.direction === 'horizontal') {
      const maxScroll = Math.max(0, content.value.offsetWidth - container.value.offsetWidth);
      scrollLeft.value = maxScroll;
    } else {
      const maxScroll = Math.max(0, content.value.offsetHeight - container.value.offsetHeight);
      scrollTop.value = maxScroll;
    }
  };

  const scrollForward = () => {
    const increment = getIncrement();
    if (props.direction === 'horizontal') {
      scrollLeft.value = Math.min(
        scrollLeft.value + increment,
        content.value.offsetWidth - container.value.offsetWidth,
      );
    } else {
      scrollTop.value = Math.min(
        scrollTop.value + increment,
        content.value.offsetHeight - container.value.offsetHeight,
      );
    }
  };

  const scrollBackward = () => {
    const increment = getIncrement();
    if (props.direction === 'horizontal') {
      scrollLeft.value = Math.max(scrollLeft.value - increment, 0);
    } else {
      scrollTop.value = Math.max(scrollTop.value - increment, 0);
    }
  };

  onMounted(() => {
    window.addEventListener('mouseup', endDrag);
  });

  onUnmounted(() => {
    window.removeEventListener('mouseup', endDrag);
    // 清理指示器定时器
    if (indicatorTimeout) {
      clearTimeout(indicatorTimeout);
    }
  });

  defineExpose({
    scrollToEnd,
    scrollForward,
    scrollBackward,
  });
</script>

<style>
  /* 动画效果 */
  @keyframes scroll-hint-pulse {
    0%,
    100% {
      transform: translateX(-50%) scale(1);
      opacity: 0.8;
    }

    50% {
      transform: translateX(-50%) scale(1.05);
      opacity: 1;
    }
  }

  @keyframes scroll-hint-pulse-vertical {
    0%,
    100% {
      transform: translateY(-50%) scale(1);
      opacity: 0.8;
    }

    50% {
      transform: translateY(-50%) scale(1.05);
      opacity: 1;
    }
  }

  /* 深色模式适配 */
  @media (prefers-color-scheme: dark) {
    .scroll-gradient-left {
      background: linear-gradient(to right, rgba(255, 255, 255, 10%), transparent);
    }

    .scroll-gradient-right {
      background: linear-gradient(to left, rgba(255, 255, 255, 10%), transparent);
    }

    .scroll-gradient-top {
      background: linear-gradient(to bottom, rgba(255, 255, 255, 10%), transparent);
    }

    .scroll-gradient-bottom {
      background: linear-gradient(to top, rgba(255, 255, 255, 10%), transparent);
    }

    .scroll-hint {
      background: rgba(255, 255, 255, 90%);
      color: #333;
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .scroll-hint {
      padding: 6px 12px;
      font-size: 11px;
    }

    .scroll-icon {
      width: 14px;
      height: 14px;
    }

    .scroll-text {
      display: none;
    }
  }

  .scrollable {
    position: relative;
  }

  .scrollable::-webkit-scrollbar {
    display: none;
  }

  /* 滚动指示器容器 */
  .scroll-indicators {
    position: absolute;
    z-index: 10;
    inset: 0;
    transition: opacity 0.3s ease;
    opacity: 0;
    pointer-events: none;
  }

  .scroll-indicators.show {
    opacity: 1;
  }

  /* 渐变遮罩 */
  .scroll-gradient {
    position: absolute;
    z-index: 11;
    pointer-events: none;
  }

  /* 水平滚动渐变遮罩 */
  .scroll-gradient-left {
    top: 0;
    bottom: 0;
    left: 0;
    width: 40px;
    background: linear-gradient(to right, rgba(0, 0, 0, 10%), transparent);
  }

  .scroll-gradient-right {
    top: 0;
    right: 0;
    bottom: 0;
    width: 40px;
    background: linear-gradient(to left, rgba(0, 0, 0, 10%), transparent);
  }

  /* 垂直滚动渐变遮罩 */
  .scroll-gradient-top {
    top: 0;
    right: 0;
    left: 0;
    height: 40px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 10%), transparent);
  }

  .scroll-gradient-bottom {
    right: 0;
    bottom: 0;
    left: 0;
    height: 40px;
    background: linear-gradient(to top, rgba(0, 0, 0, 10%), transparent);
  }

  /* 滚动提示 */
  .scroll-hint {
    position: absolute;
    z-index: 12;
    padding: 8px 16px;
    animation: scroll-hint-pulse 2s ease-in-out infinite;
    border-radius: 8px;
    background: rgba(0, 0, 0, 80%);
    color: white;
    font-size: 14px;
    backdrop-filter: blur(4px);
  }

  .scroll-hint-horizontal {
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
  }

  .scroll-hint-vertical {
    top: 50%;
    left: 12px;
    transform: translateY(-50%);
    animation: scroll-hint-pulse-vertical 2s ease-in-out infinite;
  }

  .scroll-hint-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .scroll-hint-vertical .scroll-hint-content {
    flex-direction: column;
    gap: 4px;
  }

  .scroll-icon {
    flex-shrink: 0;
    width: 16px;
    height: 16px;
    opacity: 0.8;
  }

  .scroll-text {
    font-weight: 500;
    line-height: 1;
    white-space: nowrap;
  }
</style>
