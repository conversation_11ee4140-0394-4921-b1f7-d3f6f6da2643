# public path
VITE_PUBLIC_PATH = /

# 代理路径
VITE_GLOB_API_URL=/basic-api

# 安全管控平台 代理路径
VITE_GLOB_SECURITY_API_URL=/glasses-api

# 全局加密开关(即开启了加解密功能才会生效 不是全部接口加密 需要和后端对应)
VITE_GLOB_ENABLE_ENCRYPT=true
# RSA公钥 请求加密使用 注意这两个是两对RSA公私钥 请求加密-后端解密是一对 响应解密-后端加密是一对
VITE_GLOB_RSA_PUBLIC_KEY=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==
# RSA私钥 响应解密使用 注意这两个是两对RSA公私钥 请求加密-后端解密是一对 响应解密-后端加密是一对
VITE_GLOB_RSA_PRIVATE_KEY=MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE=
# 客户端id
VITE_GLOB_APP_CLIENT_ID=e5cd7e4891bf95d1d19206ce24a7b32e

# 开启WEBSOCKET
VITE_GLOB_WEBSOCKET_ENABLE=false

# 服务代理地址
# VITE_PROXY_ORIGIN=http://192.168.50.95:8384
# VITE_PROXY_ORIGIN=http://47.98.119.23:8282
# VITE_PROXY_ORIGIN=http://42.236.74.152:8150/admin
# VITE_PROXY_ORIGIN=http://anchen.natapp1.cc/admin
# VITE_PROXY_ORIGIN=http://192.168.0.121:8182/admin
# VITE_PROXY_ORIGIN=http://192.168.0.11:8182/admin
VITE_PROXY_ORIGIN=http://192.168.0.34:8182/admin
# 安全管控平台 服务代理地址
# VITE_SECURITY_PROXY_ORIGIN=http://42.236.74.152:8150/admin
# VITE_SECURITY_PROXY_ORIGIN=http://anchen.natapp1.cc/admin
# VITE_SECURITY_PROXY_ORIGIN=http://192.168.0.121:8182/admin
# VITE_SECURITY_PROXY_ORIGIN=http://192.168.0.11:8182/admin
VITE_SECURITY_PROXY_ORIGIN=http://192.168.0.34:8182/admin
