<template>
  <div class="management-info-content">
    <div v-if="data">
      <Description
        :schema="descSchema"
        :data="data"
        :column="2"
        :bordered="true"
        size="small"
        :use-collapse="false"
      />
    </div>
    <div v-else class="empty-state">
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, h } from 'vue';
  import { Empty as AEmpty, Button as AButton, Tag as ATag } from 'ant-design-vue';
  import { Description, type DescItem } from '@/components/Description';
  import { STATUS_TEXT_MAP } from '../constants';
  import type { ManagementInfoData } from '../types';

  defineOptions({ name: 'ManagementInfoTab' });

  interface Props {
    data: ManagementInfoData | null;
    loading: boolean;
  }

  interface Emits {
    (e: 'previewImage', imageUrl: string): void;
  }

  defineProps<Props>();
  const emit = defineEmits<Emits>();

  /**
   * 状态颜色映射
   */
  const STATUS_COLOR_MAP = {
    '1': 'default', // 未确认 - 默认颜色
    '2': 'success', // 误报已确认 - 绿色
    '3': 'error', // 异常已确认 - 红色
  } as const;

  /**
   * 描述列表配置
   */
  const descSchema = computed<DescItem[]>(() => [
    {
      field: 'remark',
      label: '异常描述',
      render: (value) => value || '-',
    },
    {
      field: 'responsiblePerson',
      label: '责任人',
      render: (value) => value || '-',
    },
    {
      field: 'occurrenceTime',
      label: '发生时间',
      render: (value) => value || '-',
    },
    {
      field: 'status',
      label: '状态',
      render: (value) => getStatusTag(value),
    },
    {
      field: 'stationName',
      label: '车站名称',
      render: (value) => value || '-',
    },
    {
      field: 'annexUrl',
      label: '附件图片',
      render: (value) => {
        if (value) {
          return h(
            AButton,
            {
              type: 'link',
              size: 'small',
              onClick: () => emit('previewImage', value),
            },
            () => '查看图片',
          );
        }
        return '-';
      },
    },
  ]);

  /**
   * 获取状态标签
   */
  function getStatusTag(status?: string) {
    if (!status) return '-';

    const text = STATUS_TEXT_MAP[status as keyof typeof STATUS_TEXT_MAP];
    if (!text) return '-';

    const color = STATUS_COLOR_MAP[status as keyof typeof STATUS_COLOR_MAP] || 'default';

    return h(
      ATag,
      {
        color: color,
      },
      () => text,
    );
  }
</script>

<style scoped>
  .management-info-content {
    /* padding: 16px 0; */
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
  }
</style>
