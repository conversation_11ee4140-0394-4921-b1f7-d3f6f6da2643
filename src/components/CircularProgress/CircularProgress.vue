<template>
  <div class="circular-progress" :style="{ width: size + 'px', height: size + 'px' }">
    <div ref="chartRef" class="w-full h-full"></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, computed, type Ref } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import type { EChartsOption } from 'echarts';

  defineOptions({ name: 'CircularProgress' });

  interface Props {
    /** 进度值 (0-100) */
    value: number;
    /** 组件大小 */
    size?: number;
    /** 进度条颜色 */
    color?: string | string[];
    /** 显示标签 */
    label?: string;
    /** 是否显示数值 */
    showValue?: boolean;
    /** 进度条粗细 */
    thickness?: number;
    /** 背景色 */
    backgroundColor?: string;
    /** 字体大小 */
    fontSize?: number;
    /** 动画持续时间 */
    animationDuration?: number;
  }

  const props = withDefaults(defineProps<Props>(), {
    value: 0,
    size: 96,
    color: '#3B82F6',
    label: '',
    showValue: true,
    thickness: 12,
    backgroundColor: '#E5E7EB',
    fontSize: 14,
    animationDuration: 1000,
  });

  const chartRef = ref<HTMLDivElement>();
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 预设颜色方案
  const colorPresets = {
    cpu: ['#00a2ff', '#70ffac'],
    memory: ['#10B981', '#34D399'],
    gpu: ['#F59E0B', '#FBBF24'],
    primary: ['#3B82F6', '#60A5FA'],
    success: ['#10B981', '#34D399'],
    warning: ['#F59E0B', '#FBBF24'],
    danger: ['#EF4444', '#F87171'],
  };

  // 计算颜色
  const computedColor = computed(() => {
    if (Array.isArray(props.color)) {
      return props.color;
    }

    // 检查是否是预设颜色
    if (props.color in colorPresets) {
      return colorPresets[props.color as keyof typeof colorPresets];
    }

    return props.color;
  });

  // 创建图表配置
  const createChartOption = (): EChartsOption => {
    const color = computedColor.value;

    // 如果是数组，创建渐变色
    const progressColor = Array.isArray(color)
      ? {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0.4,
          y2: 1,
          colorStops: [
            { offset: 0, color: color[0] },
            { offset: 1, color: color[1] },
          ],
          globalCoord: false,
        }
      : color;

    // 计算内外半径
    const outerRadius = '90%';
    const innerRadius = `${90 - props.thickness}%`;

    // 按照 option.js 的核心算法，但修复超出问题
    // 前景进度值：(75 * 实际进度) / 100，但不能超过 75
    const rawProgressValue = (75 * props.value) / 100;
    const progressValue = Math.min(rawProgressValue, 75);
    // 前景剩余值：100 - 前景进度值（这里的100是pie图的数据总和）
    const remainingValue = 100 - progressValue;

    return {
      // 标题显示数值和标签，自动与圆形进度条中心对齐
      title: props.showValue
        ? {
            text: `${props.value}%`,
            left: '46%',
            top: '36%',
            textAlign: 'center',
            textStyle: {
              fontSize: props.fontSize,
              fontWeight: 'bold',
              color: '#374151',
            },
            subtext: props.label,
            subtextStyle: {
              fontSize: props.fontSize - 2,
              color: '#6B7280',
              textAlign: 'center',
            },
          }
        : undefined,
      series: [
        // 前景进度层
        {
          type: 'pie',
          center: '50%',
          radius: [innerRadius, outerRadius],
          startAngle: 225, // 从左下角开始
          color: [progressColor, 'transparent'],
          hoverAnimation: false,
          legendHoverLink: false,
          z: 10,
          label: { show: false },
          labelLine: { show: false },
          data: [
            {
              value: progressValue,
              itemStyle: {
                color: progressColor as any,
              },
            },
            {
              value: remainingValue,
              itemStyle: {
                color: 'transparent',
              },
            },
          ],
          animation: true,
          animationDuration: props.animationDuration,
          animationEasing: 'cubicOut',
        },
        // 背景层
        {
          type: 'pie',
          center: '50%',
          radius: [innerRadius, outerRadius],
          startAngle: 225,
          color: [props.backgroundColor],
          z: 9,
          hoverAnimation: false,
          legendHoverLink: false,
          label: { show: false },
          labelLine: { show: false },
          data: [
            {
              value: 75, // 显示 75% 的圆弧
              itemStyle: {
                color: props.backgroundColor,
              },
            },
            {
              value: 25, // 底部断开 25%
              itemStyle: {
                color: 'transparent',
              },
            },
          ],
        },
      ],
    } as EChartsOption;
  };

  // 更新图表
  const updateChart = () => {
    if (!chartRef.value) return;
    const option = createChartOption();
    setOptions(option);
  };

  // 监听属性变化
  watch(
    () => [props.value, props.color, props.label, props.showValue],
    () => {
      updateChart();
    },
    { deep: true },
  );

  onMounted(() => {
    updateChart();
  });

  // 暴露更新方法
  defineExpose({
    updateChart,
  });
</script>

<style scoped>
  .circular-progress {
    display: inline-block;
    position: relative;
  }
</style>
